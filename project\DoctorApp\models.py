from django.db import models
from django.core.validators import MinLengthValidator, RegexValidator
from django.utils import timezone
from django.core.exceptions import ValidationError
# Create your models here.

def validate_date_future(value):
    if value < timezone.now():
        raise ValidationError('La date de rendez-vous doit être dans le futur.')

class Doctor(models.Model):
    choix = [
        ('Cardiologue','Cardiologue'),
        ('Generaliste','Generaliste'),
        ('Dermatologue','Dermatologue'),
    ]
    doctor_id = models.CharField(max_length=100,validators=[MinLengthValidator(8), RegexValidator(regex='^\d{4}[A-Za-z]+$', message='L\'identifiant doit être unique et respecter le format suivant : 4 chiffres suivis de lettres.')])
    doctor_name = models.CharField(max_length=100, validators=[MinLengthValidator(5,message='Le nom du médecin doit contenir au moins 5 caractères.')])
    speciality_name = models.CharField(choices=choix)
    created_at = models.DateTimeField(null=True)
    updated_at = models.DateTimeField(null=True)

class Appointment(models.Model):
    doctor = models.ForeignKey(Doctor, on_delete=models.CASCADE)
    patient = models.ForeignKey('PatientApp.Patient', on_delete=models.CASCADE)
    appointment_date = models.DateTimeField(validators=[validate_date_future])
    created_at = models.DateTimeField(null=True)
    updated_at = models.DateTimeField(null=True)
