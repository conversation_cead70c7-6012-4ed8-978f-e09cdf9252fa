from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.contrib.auth.decorators import login_required

def patient_login(request):
    """
    Gère la connexion des patients
    Points: 1 (formulaire + authentification)
    """
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(request, username=username, password=password)
        
        if user is not None:
            login(request, user)
            messages.success(request, f"Bienvenue {username} !")
            return redirect('doctor:list')
        else:
            messages.error(request, "Identifiants invalides")
    
    return render(request, 'patient/login.html')

@login_required
def patient_logout(request):
    """
    Gère la déconnexion des patients
    Points: 1 (déconnexion + redirection)
    """
    logout(request)
    messages.info(request, "Vous avez été déconnecté avec succès")
    return redirect('patient:login')

@login_required
def patient_dashboard(request):
    """
    Tableau de bord patient (optionnel)
    """
    appointments = Appointment.objects.filter(
        patient=request.user
    ).select_related('doctor').order_by('-appointment_date')
    
    return render(request, 'patient/dashboard.html', {
        'appointments': appointments
    })