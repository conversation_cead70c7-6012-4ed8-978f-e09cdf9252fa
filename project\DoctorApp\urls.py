# DoctorApp/urls.py
from django.urls import path
from . import views

app_name = 'doctor'  # Namespace pour les URLs

urlpatterns = [
    # Liste des docteurs (1.5 pts)
    path('doctors/', views.doctor_list, name='list'),
    
    # Prise de rendez-vous (1.5 pts)
    path('doctors/<int:pk>/appointment/', views.create_appointment, name='create_appointment'),
    
    # Liste des rendez-vous par docteur (1 pt)
    path('appointments/<int:doctor_id>/', views.doctor_appointments, name='doctor_appointments'),
    
    # Suppression de rendez-vous (1.5 pts)
    path('appointments/delete/<int:pk>/', views.delete_appointment, name='delete_appointment'),
]