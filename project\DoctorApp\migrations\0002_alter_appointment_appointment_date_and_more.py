# Generated by Django 5.2.3 on 2025-06-30 09:42

import DoctorApp.models
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('DoctorApp', '0001_initial'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='appointment',
            name='appointment_date',
            field=models.DateTimeField(validators=[DoctorApp.models.validate_date_future]),
        ),
        migrations.AlterField(
            model_name='doctor',
            name='doctor_id',
            field=models.CharField(max_length=100, validators=[django.core.validators.MinLengthValidator(8), django.core.validators.RegexValidator(message="L'identifiant doit être unique et respecter le format suivant : 4 chiffres suivis de lettres.", regex='^\\d{4}[A-Za-z]+$')]),
        ),
        migrations.AlterField(
            model_name='doctor',
            name='doctor_name',
            field=models.Char<PERSON>ield(max_length=100, validators=[django.core.validators.MinLengthValidator(5, message='Le nom du médecin doit contenir au moins 5 caractères.')]),
        ),
    ]
