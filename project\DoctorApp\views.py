from django.shortcuts import render, redirect, get_object_or_404
from django.utils import timezone
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from .models import Doctor, Appointment

@login_required
def doctor_list(request):
    """
    Affiche la liste des docteurs ordonnée par nom
    Points: 2 (liste ordonnée + affichage)
    """
    doctors = Doctor.objects.order_by('doctor_name')
    
    # Vérifie la disponibilité pour chaque docteur
    for doctor in doctors:
        doctor.is_available = not Appointment.objects.filter(
            doctor=doctor, 
            patient=request.user
        ).exists()
    
    return render(request, 'doctor/list.html', {'doctors': doctors})

@login_required
def create_appointment(request, pk):
    """
    Crée un rendez-vous avec un docteur
    Points: 1.5 (création + date système)
    """
    doctor = get_object_or_404(Doctor, pk=pk)
    
    # Vérifie si un rendez-vous existe déjà
    if Appointment.objects.filter(doctor=doctor, patient=request.user).exists():
        messages.warning(request, f"Vous avez déjà un rendez-vous avec {doctor.doctor_name}")
    else:
        Appointment.objects.create(
            doctor=doctor,
            patient=request.user,
            appointment_date=timezone.now()
        )
        messages.success(request, f"Rendez-vous pris avec {doctor.doctor_name}")
    
    return redirect('doctor:list')

@login_required
def doctor_appointments(request, doctor_id):
    """
    Affiche les rendez-vous d'un patient avec un docteur spécifique
    Points: 1 (affichage + gestion cas vide)
    """
    doctor = get_object_or_404(Doctor, pk=doctor_id)
    appointments = Appointment.objects.filter(
        doctor=doctor,
        patient=request.user
    ).order_by('-appointment_date')
    
    return render(request, 'doctor/appointments.html', {
        'doctor': doctor,
        'appointments': appointments
    })

@login_required
def delete_appointment(request, pk):
    """
    Supprime un rendez-vous
    Points: 1.5 (suppression + sécurité)
    """
    appointment = get_object_or_404(
        Appointment, 
        pk=pk, 
        patient=request.user  # Sécurité: seul le patient peut supprimer
    )
    doctor_id = appointment.doctor.id
    appointment.delete()
    messages.success(request, "Rendez-vous supprimé avec succès")
    return redirect('doctor:doctor_appointments', doctor_id=doctor_id)